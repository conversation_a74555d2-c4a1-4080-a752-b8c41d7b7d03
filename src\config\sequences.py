#!/usr/bin/env python3
"""Sequence configuration and series management."""

# Default sequences for different purposes
DEFAULT_SEQUENCES = {
    "general": "1000",
    "analysis": "1750",
    "enhancement": "1300",
    "critique": "1900"
}

# Series configuration based on step patterns
SERIES_CONFIG = {
    0: {
        "name": "Special Instructions",
        "range": (0, 999),
        "pattern": "a",
        "description": "Special instructions (single step)"
    },
    1000: {
        "name": "Single-Step Instructions",
        "range": (1000, 1999),
        "pattern": "a",
        "description": "Single-step instructions (just a)"
    },
    2000: {
        "name": "Double-Step Instructions",
        "range": (2000, 2999),
        "pattern": "a-b",
        "description": "Double-step instructions (a-b)"
    },
    3000: {
        "name": "Triple-Step Instructions",
        "range": (3000, 3999),
        "pattern": "a-c",
        "description": "Triple-step instructions (a-c)"
    },
    4000: {
        "name": "Quad-Step Instructions",
        "range": (4000, 4999),
        "pattern": "a-d",
        "description": "Quad-step instructions (a-d)"
    }
}

def get_series_name(series_num):
    """Get descriptive name for a series."""
    config = SERIES_CONFIG.get(series_num)
    if config:
        return f"Series {series_num}: {config['name']} ({config['range'][0]}-{config['range'][1]})"
    return f"Series {series_num}"

def get_series_from_id(seq_id):
    """Extract series number from sequence ID."""
    try:
        num = int(seq_id)
        for series, config in SERIES_CONFIG.items():
            start, end = config["range"]
            if start <= num <= end:
                return series
        return 0
    except (ValueError, TypeError):
        return 0
