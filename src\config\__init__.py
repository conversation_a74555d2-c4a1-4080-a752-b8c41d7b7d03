#!/usr/bin/env python3
"""
Centralized configuration for AI Systems
Maintains backward compatibility while providing clean organization
"""

# Import all configuration from organized modules
from .models import (
    MODEL_REGISTRY, MOD<PERSON>_CONFIG, PROVIDERS, DEFAULT_PROVIDER,
    AVAILABLE_MODELS, DEFAULT_PROVIDER_MODELS, PROVIDER_ANTHROPIC,
    PROVIDER_DEEPSEEK, PROVIDER_GOOGLE, PROVIDER_OPENAI,
    get_default_model, get_available_models, get_model_params
)

from .paths import (
    SCRIPT_DIR, DEFAULT_OUTPUT_DIR, set_default_output_dir
)

from .sequences import (
    DEFAULT_SEQUENCES, SERIES_CONFIG,
    get_series_name, get_series_from_id
)

from .execution import (
    EXECUTION_DEFAULTS, LITELLM_CONFIG, configure_litellm
)

from .files import (
    FILENAME_PATTERNS, FILENAME_SANITIZATION
)

from .interactive import INTERACTIVE_CONFIG

from .templates import CA<PERSON><PERSON><PERSON>G_CONFIG

from .utils import (
    ensure_utf8_encoding,
    get_stage_name, get_stage_from_id  # Backward compatibility
)

# Backward compatibility - export everything that was previously available
__all__ = [
    # Model configuration
    'MODEL_REGISTRY', 'MODEL_CONFIG', 'PROVIDERS', 'DEFAULT_PROVIDER',
    'AVAILABLE_MODELS', 'DEFAULT_PROVIDER_MODELS', 'PROVIDER_ANTHROPIC',
    'PROVIDER_DEEPSEEK', 'PROVIDER_GOOGLE', 'PROVIDER_OPENAI',
    'get_default_model', 'get_available_models', 'get_model_params',
    
    # Path configuration
    'SCRIPT_DIR', 'DEFAULT_OUTPUT_DIR', 'set_default_output_dir',
    
    # Sequence configuration
    'DEFAULT_SEQUENCES', 'SERIES_CONFIG', 'get_series_name', 'get_series_from_id',
    
    # Execution configuration
    'EXECUTION_DEFAULTS', 'LITELLM_CONFIG', 'configure_litellm',
    
    # File configuration
    'FILENAME_PATTERNS', 'FILENAME_SANITIZATION',
    
    # Interactive configuration
    'INTERACTIVE_CONFIG',
    
    # Template configuration
    'CATALOG_CONFIG',
    
    # Utility functions
    'ensure_utf8_encoding', 'get_stage_name', 'get_stage_from_id'
]
