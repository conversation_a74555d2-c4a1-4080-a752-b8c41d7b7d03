#!/usr/bin/env python3
"""
Batch Execution System for AI Systems
Allows concurrent execution of multiple sequences through JSON configuration
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator

from main import execute_sequence, ExecutorConfig, TemplateCatalog, SequenceManager, configure_litellm
from config import EXECUTION_DEFAULTS, FILENAME_PATTERNS


# =============================================================================
# BATCH CONFIGURATION MODELS
# =============================================================================

class BatchConfig(BaseModel):
    """Complete batch execution configuration."""
    name: str = Field(description="Batch execution name (used as output directory)")
    description: Optional[str] = Field(default=None, description="Batch description")

    # Configuration section (new format)
    config: Optional[Dict[str, Any]] = Field(default=None, description="Execution configuration")

    # Global defaults
    defaults: Dict[str, Any] = Field(default_factory=dict, description="Default execution parameters")

    # Jobs to execute - can be either list or dict format
    jobs: Union[List[Dict[str, Any]], Dict[str, Dict[str, Any]]] = Field(description="Jobs to execute")

    # Execution options
    concurrent: bool = Field(default=True, description="Run jobs concurrently")
    max_concurrent: Optional[int] = Field(default=None, description="Maximum concurrent jobs")
    
    @validator('jobs')
    def validate_jobs(cls, v):
        if not v:
            raise ValueError("At least one job must be specified")
        
        # Handle both list and dict formats
        if isinstance(v, dict):
            # For dict format, validate that all values are valid job configurations
            for job_key, job_data in v.items():
                if not isinstance(job_data, dict):
                    raise ValueError(f"Job '{job_key}' must be a dictionary")
                # Validate required fields
                if 'sequence' not in job_data:
                    raise ValueError(f"Job '{job_key}' missing required 'sequence' field")
        elif isinstance(v, list):
            # For list format, validate using existing logic
            if len(v) == 0:
                raise ValueError("At least one job must be specified")
            
            # Check for duplicate job names (only for non-None names)
            names = [job.get('name') for job in v if job.get('name') is not None]
            if len(names) != len(set(names)):
                raise ValueError("Job names must be unique")
        
        return v


# =============================================================================
# BATCH EXECUTOR
# =============================================================================

class BatchExecutor:
    """Executes multiple sequences concurrently based on JSON configuration."""
    
    def __init__(self, config_path: str):
        self.config_path = Path(config_path)
        self.batch_config: Optional[BatchConfig] = None
        self.catalog = None
        self.batch_output_dir: Optional[Path] = None
        
    def load_config(self) -> BatchConfig:
        """Load and validate batch configuration from JSON file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Batch config file not found: {self.config_path}")

        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Transform new format to existing format before Pydantic validation
            config_data = self._normalize_config_format(config_data)

            self.batch_config = BatchConfig(**config_data)
            return self.batch_config

        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in batch config: {e}")
        except Exception as e:
            raise ValueError(f"Invalid batch configuration: {e}")

    def _normalize_config_format(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform new job format to existing List[Dict] format."""
        
        # Handle config section - merge with defaults section and top level
        if "config" in config_data:
            config_section = config_data["config"]
            # Merge config settings into defaults, config takes precedence
            if "defaults" not in config_data:
                config_data["defaults"] = {}
            for key, value in config_section.items():
                config_data["defaults"][key] = value
                # Also merge execution control fields to top level
                if key in ["concurrent", "max_concurrent"]:
                    config_data[key] = value

        # Transform jobs if they're in object format
        if isinstance(config_data.get("jobs"), dict):
            defaults = config_data.get("defaults", {})
            job_list = []

            for job_key, job_data in config_data["jobs"].items():
                # Apply defaults, then job overrides
                final_job = {**defaults, **job_data}

                # Use key as name if not specified
                if "name" not in final_job:
                    final_job["name"] = job_key

                job_list.append(final_job)

            config_data["jobs"] = job_list

        return config_data

    def _get_job_list(self) -> List[Dict[str, Any]]:
        """Get jobs as a list, handling both formats."""
        if isinstance(self.batch_config.jobs, list):
            # Already in list format, return as is
            return self.batch_config.jobs
        else:
            # Dict format, already normalized
            return self.batch_config.jobs

    def _setup_output_directory(self):
        """Create the batch output directory structure."""
        # Create batch-specific output directory
        batch_name = self.batch_config.name
        self.batch_output_dir = Path("src/output") / batch_name
        
        # Create the main batch directory
        self.batch_output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Batch output directory: {self.batch_output_dir}")

    def _get_model_directory(self, models: List[str]) -> Path:
        """Get or create model-specific subdirectory."""
        # Create a directory name from the models
        if len(models) == 1:
            model_dir_name = models[0].replace('/', '-').replace(':', '-')
        else:
            # For multiple models, create a combined name
            model_dir_name = '+'.join([m.replace('/', '-').replace(':', '-') for m in models])
        
        model_dir = self.batch_output_dir / model_dir_name
        model_dir.mkdir(exist_ok=True)
        return model_dir

    def _format_sequence_suffix(self, sequence: str) -> str:
        """Format sequence specification for filename suffix."""
        # Replace pipe separators with plus signs for filename compatibility
        return sequence.replace('|', '+')

    def load_catalog(self):
        """Load template catalog."""
        self.catalog = TemplateCatalog.load_catalog()
        if not self.catalog:
            raise RuntimeError("Failed to load template catalog")
    
    async def execute_job(self, job_data: Dict[str, Any], job_index: int) -> Dict[str, Any]:
        """Execute a single batch job."""
        job_name = job_data.get('name', f'job_{job_index + 1}')
        print(f"\n🚀 Starting job '{job_name}' ({job_index + 1}/{len(self._get_job_list())})")
        
        try:
            # Resolve sequence steps
            sequence_steps = SequenceManager.resolve_sequence_specification(
                self.catalog, job_data['sequence']
            )
            
            if not sequence_steps:
                raise ValueError(f"Sequence '{job_data['sequence']}' not found")
            
            # Apply defaults and job-specific settings
            defaults = self.batch_config.defaults

            # Resolve required fields with defaults
            user_prompt = job_data.get('prompt') if job_data.get('prompt') is not None else defaults.get('prompt')
            models = job_data.get('models') if job_data.get('models') is not None else defaults.get('models', [])

            # Validate required fields
            if not user_prompt:
                raise ValueError(f"No prompt specified for job '{job_name}' and no default prompt provided")
            if not models:
                raise ValueError(f"No models specified for job '{job_name}' and no default models provided")

            # Get model-specific output directory
            model_dir = self._get_model_directory(models)
            
            # Generate output filename with sequence information
            timestamp = datetime.now().strftime(FILENAME_PATTERNS["timestamp_format"])
            # Use centralized sequence prefix for consistency
            sequence_suffix = f"{FILENAME_PATTERNS['sequence_prefix']}-{self._format_sequence_suffix(job_data['sequence'])}"
            
            if job_data.get('output_prefix'):
                output_filename = FILENAME_PATTERNS["batch_with_prefix"].format(
                    output_prefix=job_data['output_prefix'],
                    job_name=job_name.replace(' ', '_'),
                    timestamp=timestamp,
                    sequence_suffix=sequence_suffix
                )
            else:
                output_filename = FILENAME_PATTERNS["batch"].format(
                    job_name=job_name.replace(' ', '_'),
                    timestamp=timestamp,
                    sequence_suffix=sequence_suffix
                )

            output_path = model_dir / output_filename

            # Create executor configuration
            config = ExecutorConfig(
                sequence_steps=sequence_steps,
                user_prompt=user_prompt,
                sequence_id=job_data['sequence'],
                models=models,
                output_file=str(output_path),
                system_instruction_extractor=TemplateCatalog.get_system_instruction,
                
                # Execution parameters (job overrides defaults, defaults override EXECUTION_DEFAULTS)
                chain_mode=job_data.get('chain_mode') if job_data.get('chain_mode') is not None else defaults.get('chain_mode', EXECUTION_DEFAULTS['chain_mode']),
                show_inputs=job_data.get('show_inputs') if job_data.get('show_inputs') is not None else defaults.get('show_inputs', EXECUTION_DEFAULTS['show_inputs']),
                show_system_instructions=job_data.get('show_system_instructions') if job_data.get('show_system_instructions') is not None else defaults.get('show_system_instructions', EXECUTION_DEFAULTS['show_system_instructions']),
                show_responses=job_data.get('show_responses') if job_data.get('show_responses') is not None else defaults.get('show_responses', EXECUTION_DEFAULTS['show_responses']),
                minified_output=job_data.get('minified_output') if job_data.get('minified_output') is not None else defaults.get('minified_output', EXECUTION_DEFAULTS['minified_output']),
                
                # Optional parameters
                aggregator=job_data.get('aggregator'),
                aggregator_inputs=job_data.get('aggregator_inputs'),
                temperature=job_data.get('temperature') if job_data.get('temperature') is not None else defaults.get('temperature'),
                max_tokens=job_data.get('max_tokens') if job_data.get('max_tokens') is not None else defaults.get('max_tokens')
            )
            
            # Execute the sequence
            results = await execute_sequence(config=config)
            
            print(f"✅ Completed job '{job_name}'")
            print(f"📁 Output saved to: {output_path}")
            
            return {
                'job_name': job_name,
                'status': 'success',
                'output_file': str(output_path),
                'sequence': job_data['sequence'],
                'models': models,
                'steps_executed': len(results) if results else 0
            }
            
        except Exception as e:
            print(f"❌ Failed job '{job_name}': {e}")
            return {
                'job_name': job_name,
                'status': 'failed',
                'error': str(e),
                'sequence': job_data['sequence'],
                'models': job_data.get('models', [])
            }
    
    async def execute_batch(self) -> Dict[str, Any]:
        """Execute all jobs in the batch configuration."""
        if not self.batch_config:
            raise RuntimeError("Batch configuration not loaded")
        
        print(f"\n🎯 Starting batch execution: {self.batch_config.name}")
        if self.batch_config.description:
            print(f"📝 Description: {self.batch_config.description}")
        
        # Setup output directory structure
        self._setup_output_directory()
        
        job_list = self._get_job_list()
        print(f"📊 Total jobs: {len(job_list)}")
        print(f"⚡ Concurrent execution: {'Yes' if self.batch_config.concurrent else 'No'}")
        
        if self.batch_config.max_concurrent:
            print(f"🔢 Max concurrent: {self.batch_config.max_concurrent}")
        
        start_time = datetime.now()
        
        if self.batch_config.concurrent:
            # Execute jobs concurrently
            if self.batch_config.max_concurrent:
                # Use semaphore to limit concurrency
                semaphore = asyncio.Semaphore(self.batch_config.max_concurrent)
                
                async def execute_with_semaphore(job, index):
                    async with semaphore:
                        return await self.execute_job(job, index)
                
                tasks = [
                    execute_with_semaphore(job, i) 
                    for i, job in enumerate(job_list)
                ]
            else:
                # No limit on concurrency
                tasks = [
                    self.execute_job(job, i) 
                    for i, job in enumerate(job_list)
                ]
            
            job_results = await asyncio.gather(*tasks, return_exceptions=True)
        else:
            # Execute jobs sequentially
            job_results = []
            for i, job in enumerate(job_list):
                result = await self.execute_job(job, i)
                job_results.append(result)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Process results
        successful_jobs = [r for r in job_results if isinstance(r, dict) and r.get('status') == 'success']
        failed_jobs = [r for r in job_results if isinstance(r, dict) and r.get('status') == 'failed']
        exception_jobs = [r for r in job_results if isinstance(r, Exception)]
        
        # Save batch results in the batch directory
        timestamp = datetime.now().strftime(FILENAME_PATTERNS["timestamp_format"])
        batch_results_file = self.batch_output_dir / FILENAME_PATTERNS["batch_results"].format(timestamp=timestamp)
        
        batch_summary = {
            'batch_name': self.batch_config.name,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'total_jobs': len(job_list),
            'successful_jobs': len(successful_jobs),
            'failed_jobs': len(failed_jobs) + len(exception_jobs),
            'job_results': job_results
        }
        
        with open(batch_results_file, 'w', encoding='utf-8') as f:
            json.dump(batch_summary, f, indent=2, ensure_ascii=False)
        
        # Summary
        print(f"\n📈 Batch execution completed!")
        print(f"⏱️  Total duration: {duration}")
        print(f"✅ Successful jobs: {len(successful_jobs)}")
        print(f"❌ Failed jobs: {len(failed_jobs) + len(exception_jobs)}")
        print(f"📁 Batch results saved to: {batch_results_file}")
        
        if failed_jobs:
            print(f"\n❌ Failed jobs:")
            for job in failed_jobs:
                print(f"  - {job['job_name']}: {job['error']}")
        
        if exception_jobs:
            print(f"\n💥 Jobs with exceptions:")
            for i, exc in enumerate(exception_jobs):
                print(f"  - Job {i}: {exc}")
        
        return batch_summary


# =============================================================================
# CLI INTERFACE
# =============================================================================

async def main():
    """CLI entry point for batch execution."""
    if len(sys.argv) != 2:
        print("Usage: python src/batch_executor.py <batch_config.json>")
        sys.exit(1)
    
    config_path = sys.argv[1]
    
    try:
        # Initialize and configure
        configure_litellm()
        
        # Create and run batch executor
        executor = BatchExecutor(config_path)
        executor.load_config()
        executor.load_catalog()
        
        # Execute batch
        results = await executor.execute_batch()
        
        print(f"\n🎉 Batch execution completed successfully!")
        
    except Exception as e:
        print(f"❌ Batch execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
