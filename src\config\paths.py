#!/usr/bin/env python3
"""Path configuration and management."""

from pathlib import Path

def get_script_dir():
    """Get current script directory (main src directory, not config subdirectory)."""
    # Use the same approach as BaseGenerator: Path(__file__).parent.parent
    return Path(__file__).parent.parent

# Default paths
SCRIPT_DIR = get_script_dir()
DEFAULT_OUTPUT_DIR = SCRIPT_DIR / "output"

def set_default_output_dir(directory: str):
    """Set default output directory."""
    global DEFAULT_OUTPUT_DIR
    DEFAULT_OUTPUT_DIR = Path(directory)
