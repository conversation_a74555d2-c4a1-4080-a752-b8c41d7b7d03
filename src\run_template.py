#!/usr/bin/env python3
"""
Universal template runner - eliminates boilerplate from all template files.
Usage: python run_template.py <template_file.py>
"""

import sys
from pathlib import Path
from processor import BaseGenerator

def main():
    if len(sys.argv) != 2:
        print("Usage: python run_template.py <template_file.py>")
        sys.exit(1)
    
    template_file = Path(sys.argv[1])
    if not template_file.exists():
        print(f"Error: Template file {template_file} not found")
        sys.exit(1)
    
    BaseGenerator.run_from_file(template_file)

if __name__ == "__main__":
    main()
