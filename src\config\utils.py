#!/usr/bin/env python3
"""Utility functions and backward compatibility aliases."""

from .execution import ensure_utf8_encoding

# Backward compatibility aliases (deprecated)
def get_stage_name(stage_num):
    """Deprecated: Use get_series_name instead."""
    from .sequences import get_series_name
    return get_series_name(stage_num)

def get_stage_from_id(seq_id):
    """Deprecated: Use get_series_from_id instead."""
    from .sequences import get_series_from_id
    return get_series_from_id(seq_id)
