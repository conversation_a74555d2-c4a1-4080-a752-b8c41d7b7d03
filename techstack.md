# Technology Stack

## Core Framework
- **Python 3.9+** - Primary language
- **AsyncIO** - Asynchronous execution engine
- **UV** - Modern Python package manager

## LLM Integration
- **LiteLLM** - Multi-provider LLM abstraction layer
- **OpenAI API** - GPT models
- **Anthropic API** - Claude models  
- **OpenRouter API** - Model routing
- **Tiktoken** - Token counting

## Data & Validation
- **Pydantic** - Data validation and serialization
- **JSON** - Configuration and output format

## HTTP & Networking
- **AIOHTTP** - Async HTTP client
- **HTTPX** - Modern HTTP client

## CLI & Display
- **Argparse** - Command-line interface
- **Rich** - Terminal formatting and display

## Build & Packaging
- **Setuptools** - Package building
- **Wheel** - Distribution format

## Architecture Pattern
- **Template-driven execution** - Python generators → Markdown → JSON → Runtime
- **Modular configuration** - Centralized config system
- **Batch processing** - Concurrent/sequential execution modes
- **Sequence chaining** - Multi-step instruction pipelines
