#!/usr/bin/env python3
"""
Constants module for AI Systems
Centralizes all hardcoded values, magic numbers, and string literals
"""

# =============================================================================
# DISPLAY CONSTANTS
# =============================================================================

# Separator patterns
SEPARATOR_LINE = "=" * 60
SEPARATOR_SHORT = "=" * 30
SEPARATOR_LONG = "=" * 80

# Performance display formatting
PERFORMANCE_PRECISION = 3  # Decimal places for timing metrics

# Template validation constants
TEMPLATE_PREVIEW_LIMIT = 3  # Number of templates to show in previews
REQUIRED_TEMPLATE_FIELDS = ["title", "interpretation", "transformation"]
OPTIONAL_TEMPLATE_FIELDS = ["context", "test", "examples"]

# Template schema definition
TEMPLATE_SCHEMA = {
    "title": {
        "type": str,
        "required": True,
        "description": "Brief, descriptive name for the template"
    },
    "interpretation": {
        "type": str,
        "required": True,
        "description": "Goal statement explaining what the template does"
    },
    "transformation": {
        "type": str,
        "required": True,
        "description": "Backticked transformation specification"
    },
    "context": {
        "type": dict,
        "required": False,
        "default": {},
        "description": "Additional context or metadata"
    }
}

# =============================================================================
# REGEX PATTERNS
# =============================================================================

# Template ID pattern for sequence extraction
TEMPLATE_ID_PATTERN = r"(\d+)-([a-z])-(.+)"
TEMPLATE_ID_GROUPS = {
    "id": 1,
    "step": 2,
    "name": 3
}

# Step ordering function


def step_order(step: str) -> int:
    """Convert step letter to numeric order (a=0, b=1, etc.)"""
    return ord(step) - ord('a')


# =============================================================================
# FILE SYSTEM CONSTANTS
# =============================================================================

# File extensions
TEMPLATE_EXTENSION = "md"
CATALOG_EXTENSION = "json"
PYTHON_EXTENSION = "py"

# Directory names
GENERATED_DIR = "generated"
TEMPLATES_DIR = "templates"
OUTPUT_DIR = "output"
CONFIG_DIR = "config"

# =============================================================================
# EXECUTION CONSTANTS
# =============================================================================

# Default timeout values (in seconds)
DEFAULT_REQUEST_TIMEOUT = 30
DEFAULT_RETRY_COUNT = 2
DEFAULT_CONNECTION_TIMEOUT = 10

# Connection pooling limits
MAX_KEEPALIVE_CONNECTIONS = 20
MAX_TOTAL_CONNECTIONS = 100
KEEPALIVE_EXPIRY = 30

# =============================================================================
# VALIDATION CONSTANTS
# =============================================================================

# Series validation ranges
SERIES_RANGES = {
    0: (0, 999),
    1000: (1000, 1999),
    2000: (2000, 2999),
    3000: (3000, 3999),
    4000: (4000, 4999),
    5000: (5000, 5999),
    6000: (6000, 6999),
    7000: (7000, 7999),
    8000: (8000, 8999),
    9000: (9000, 9999)
}

# =============================================================================
# ERROR MESSAGES
# =============================================================================

ERROR_MESSAGES = {
    "catalog_not_loaded": "Template catalog not loaded",
    "sequence_not_found": "Sequence '{}' not found in catalog",
    "template_missing_fields": "Template {} missing required fields: {}",
    "invalid_template_format": "Template for step {} has invalid format",
    "batch_config_not_loaded": "Batch configuration not loaded",
    "generator_execution_failed": "Generator {} execution failed"
}

# Success messages
SUCCESS_MESSAGES = {
    "template_processed": "SUCCESS: Processed {}",
    "generator_completed": "✅ {} completed",
    "batch_execution_complete": "✅ All template generation completed!",
    "stage_compliance_success": "SUCCESS: All templates are stage-compliant"
}

# =============================================================================
# UNICODE SYMBOLS
# =============================================================================

SYMBOLS = {
    "rocket": "🚀",
    "gear": "🔧",
    "folder": "📁",
    "refresh": "🔄",
    "check": "✅",
    "cross": "❌",
    "target": "🎯",
    "memo": "📝",
    "chart": "📊",
    "lightning": "⚡",
    "numbers": "🔢",
    "bulb": "💡",
    "wave": "👋"
}

# =============================================================================
# ENCODING CONSTANTS
# =============================================================================

DEFAULT_ENCODING = "utf-8"
ENCODING_ERROR_HANDLING = "replace"
