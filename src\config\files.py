#!/usr/bin/env python3
"""File naming patterns and sanitization configuration."""

# Output filename patterns
FILENAME_PATTERNS = {
    "history": "history--{timestamp}--{sequence_id}--{model_tag}.json",
    "batch": "batch--{job_name}--{timestamp}--{sequence_suffix}.json",
    "batch_with_prefix": "{output_prefix}--{job_name}--{timestamp}--{sequence_suffix}.json",
    "batch_results": "batch_results--{timestamp}.json",
    "timestamp_format": "%Y.%m.%d-kl.%H.%M.%S",
    "sequence_prefix": "SEQ",
    "text_prefix": "TEXT"
}

# File sanitization replacements
FILENAME_SANITIZATION = {
    "|": "+", ":": "-", "?": "_", "*": "_",
    "<": "_", ">": "_", "\"": "_", "/": "_", "\\": "_"
}
