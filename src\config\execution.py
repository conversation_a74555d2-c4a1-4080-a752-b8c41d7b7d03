#!/usr/bin/env python3
"""Execution configuration and LiteLLM settings."""

import sys

# Default execution settings
EXECUTION_DEFAULTS = {
    "chain_mode": True,
    "show_inputs": False,
    "show_system_instructions": False,
    "show_responses": True,
    "show_performance": False,
    "minified_output": False,
    "temperature": None,
    "max_tokens": None
}

# LiteLLM configuration
LITELLM_CONFIG = {
    "drop_params": True,
    "num_retries": 2,  # Reduced from 3 for faster failure
    "request_timeout": 30,  # Reduced from 120 for responsiveness
    "set_verbose": False,
    "callbacks": []
}

def ensure_utf8_encoding():
    """Set UTF-8 output encoding for terminals."""
    for stream in (sys.stdout, sys.stderr):
        if hasattr(stream, "reconfigure"):
            try:
                stream.reconfigure(encoding="utf-8", errors="replace")
            except Exception:
                pass

def configure_litellm():
    """Configure LiteLLM settings with performance optimizations."""
    import litellm
    
    # Basic LiteLLM configuration
    litellm.set_verbose = False
    litellm.callbacks = []         # Disable default callbacks

    # Performance optimizations
    litellm.num_retries = LITELLM_CONFIG["num_retries"]
    litellm.request_timeout = LITELLM_CONFIG["request_timeout"]

    # Connection pooling (if supported by underlying HTTP client)
    try:
        import httpx
        # Configure connection limits for better performance
        litellm.client = httpx.AsyncClient(
            limits=httpx.Limits(
                max_keepalive_connections=20,
                max_connections=100,
                keepalive_expiry=30
            ),
            timeout=httpx.Timeout(LITELLM_CONFIG["request_timeout"])
        )
    except ImportError:
        pass  # Fall back to default client if httpx not available

    ensure_utf8_encoding()
