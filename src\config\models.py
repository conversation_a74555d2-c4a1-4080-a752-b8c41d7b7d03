#!/usr/bin/env python3
"""Model configuration and registry management."""

# Model Registry (maps user-friendly names to actual LiteLLM model IDs)
MODEL_REGISTRY = {
    # OpenAI
    "gpt-3.5-turbo": "gpt-3.5-turbo",
    "gpt-3.5-turbo-instruct": "gpt-3.5-turbo-instruct",
    "gpt-4o": "gpt-4o",
    "gpt-4": "gpt-4",
    "gpt-4-turbo": "gpt-4-turbo",
    "gpt-4.1": "gpt-4.1",
    "o3-mini": "o3-mini",
    "gpt-5": "gpt-5",
    "gpt-5-mini": "gpt-5-mini",
    "gpt-5-nano": "gpt-5-nano",
    "gpt-5-chat": "gpt-3.5-turbo",
    "gpt-5-chat-latest": "gpt-3.5-turbo",
    # Anthropic
    "claude-3-opus": "anthropic/claude-3-opus-20240229",
    "claude-3-sonnet": "anthropic/claude-3-sonnet-20240229",
    "claude-3-haiku": "anthropic/claude-3-haiku-20240307",
    "claude-3.7-sonnet": "openrouter/anthropic/claude-3.7-sonnet:beta",
    "claude-opus-4-20250514": "claude-opus-4-20250514",
    "claude-sonnet-4-20250514": "claude-sonnet-4-20250514",
    # Google
    "gemini-pro": "gemini/gemini-1.5-pro",
    "gemini-flash": "gemini/gemini-1.5-flash-latest",
    "gemini-2-flash": "gemini/gemini-2.0-flash",
    "gemini-2.5-pro": "gemini/gemini-2.5-pro-preview-03-25",
    # Deepseek
    "deepseek-reasoner": "deepseek/deepseek-reasoner",
    "deepseek-coder": "deepseek/deepseek-coder",
    "deepseek-chat": "deepseek/deepseek-chat",
    # Others
    "kimi-k2-instruct": "groq/moonshotai/kimi-k2-instruct",
}

# Provider Selection
PROVIDERS = {
    "anthropic": "anthropic",
    "deepseek": "deepseek",
    "google": "google",
    "openai": "openai"
}

# Default provider
DEFAULT_PROVIDER = "openai"

# Provider constants for backward compatibility
PROVIDER_ANTHROPIC = "anthropic"
PROVIDER_DEEPSEEK = "deepseek"
PROVIDER_GOOGLE = "google"
PROVIDER_OPENAI = "openai"

# Complete model configuration with provider-specific settings
MODEL_CONFIG = {
    "anthropic": {
        "models": [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            "openrouter/anthropic/claude-3.7-sonnet:beta",
            "claude-sonnet-4-20250514",
        ],
        "default": "openrouter/anthropic/claude-3.7-sonnet:beta",
        "fallback": "anthropic/claude-3-haiku-20240307"
    },
    "deepseek": {
        "models": [
            "deepseek/deepseek-reasoner",
            "deepseek/deepseek-coder",
            "deepseek/deepseek-chat"
        ],
        "default": "deepseek/deepseek-chat",
        "fallback": "deepseek/deepseek-chat"
    },
    "google": {
        "models": [
            "gemini/gemini-1.5-flash-latest",
            "gemini/gemini-2.0-flash",
            "gemini/gemini-2.5-pro-preview-03-25"
        ],
        "default": "gemini/gemini-2.5-pro-preview-03-25",
        "fallback": "gemini/gemini-1.5-flash-latest"
    },
    "openai": {
        "models": [
            "gpt-4o",
            "gpt-4o-mini",
            "gpt-3.5-turbo-instruct",
            "gpt-3.5-turbo-1106",
            "o3-mini",
            "gpt-3.5-turbo",
            "gpt-4.1"
        ],
        "fallback": "gpt-4.1",
        "default": "gpt-3.5-turbo"
    }
}

# Backward compatibility mappings
AVAILABLE_MODELS = {provider: config["models"] for provider, config in MODEL_CONFIG.items()}
DEFAULT_PROVIDER_MODELS = {
    provider: {"model_name": config["default"]} for provider, config in MODEL_CONFIG.items()
}

def get_default_model(provider=None):
    """Get default model for a provider."""
    provider = provider or DEFAULT_PROVIDER
    if provider in MODEL_CONFIG:
        return MODEL_CONFIG[provider]["default"]
    return MODEL_CONFIG["openai"]["default"]

def get_available_models():
    """Get models grouped by provider."""
    result = {}
    for provider, config in MODEL_CONFIG.items():
        default_model = config["default"]
        provider_models = []

        for model_name in config["models"]:
            model_id = MODEL_REGISTRY.get(model_name, model_name)
            provider_models.append({
                "name": model_name,
                "model_id": model_id,
                "is_default": (model_name == default_model or model_id == default_model)
            })

        result[provider] = provider_models

    return result

def get_model_params(model_name=None, provider=None):
    """Resolve model name to LiteLLM ID with parameters."""
    provider = provider or DEFAULT_PROVIDER
    model_name = model_name or get_default_model(provider)

    # Get model ID from registry or use as-is
    actual_model_id = MODEL_REGISTRY.get(model_name, model_name)

    # Return model parameters
    return {"model": actual_model_id}
