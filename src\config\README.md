# Configuration Structure

The configuration has been reorganized into logical, focused modules for better maintainability and clarity.

## Structure

```
src/config/
├── __init__.py          # Main package init - exports everything
├── models.py            # Model registry and provider configuration
├── paths.py             # Path and directory configuration
├── sequences.py         # Sequence and series configuration
├── execution.py         # Execution defaults and LiteLLM settings
├── files.py             # File naming patterns and sanitization
├── interactive.py       # Interactive CLI configuration
├── templates.py         # Template system configuration
└── utils.py             # Utility functions and backward compatibility
```

## Benefits

- **Logical Organization**: Related configuration is grouped together
- **Easier Maintenance**: Changes to specific areas are isolated
- **Better Readability**: Each file has a single, clear purpose
- **100% Backward Compatibility**: All existing imports continue to work
- **Cleaner Imports**: Can import specific config areas if needed

## Usage

### Standard Import (Recommended)
```python
from config import FILENAME_PATTERNS, get_default_model
```

### Specific Module Import (Advanced)
```python
from config.models import MODEL_REGISTRY
from config.files import FILENAME_PATTERNS
```

### Backward Compatibility
All existing code continues to work without changes:
```python
from config import *  # Still works exactly as before
```

## Migration

No migration required! The old `config.py` now redirects to the new organized structure, maintaining 100% backward compatibility.

## Adding New Configuration

1. **Identify the appropriate module** for your configuration
2. **Add the configuration** to that module
3. **Export it** in the module's `__init__.py` if needed
4. **Update this README** to document the new configuration

## Example: Adding a New Model

```python
# In src/config/models.py
MODEL_REGISTRY["new-model"] = "actual/model/id"

# In src/config/models.py MODEL_CONFIG
"openai": {
    "models": [
        # ... existing models ...
        "new-model"
    ],
    # ... rest of config
}
```
