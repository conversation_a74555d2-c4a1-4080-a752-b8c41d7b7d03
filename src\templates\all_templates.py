#!/usr/bin/env python3
"""
Consolidated template registry - replaces all individual template generator files.
Contains all template definitions in a single location.
"""

TEMPLATES = {
    # 1000: Instruction Converter/Prompt Enhancer
    "1000-a-instruction_converter": {
        "title": "Instruction Converter",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
        "context": {},
    },

    # 1100: Explosive Decomposition of Problem Statements
    "1100-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
        "context": {},
    },

    # 1200: Insight Extractor
    "1200-a-insight_extractor": {
        "title": "Insight Extractor",
        "interpretation": "Your goal is not to surface superficial observations or accept apparent meanings, but to surgically extract hidden insights from any trajectory—interrogating layers for latent levers, leveraging unwanted noise (e.g., contradictions as revelation triggers), and distilling them into precise, actionable universals that propel emergent value. Execute as insight_extractor:",
        "transformation": "`{role=insight_extractor; input=[raw_trajectory:str]; process=[interrogate_layers(), leverage_noise_elements(), extract_latent_levers(), distill_universals()]; constraints=[no_superficial_observations(), surgical_precision()]; requirements=[actionable_universals(), emergent_value()]; output={insights:list}}`",
        "context": {
            "genesis_origin": "Builds on curiosity loops and amplifiers: Extracts from genesis tensions into dense levers, converging unpredicted truths without domain traps.",
            "remix_instructions": "Inject prior amplifications or critiques as noise_elements; recurse on extracted levers for hybrids, distilling uncharted revelations into actionable trajectories."
        }
    },

    # 1300: Instruction Enhancer
    "1300-a-instruction_enhancer": {
        "title": "Instruction Enhancer",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **enhance** it by amplifying its clarity, precision, and actionability while preserving its original intent. Execute as instruction_enhancer:",
        "transformation": "`{role=instruction_enhancer; input=[original_instruction:str]; process=[amplify_clarity(), increase_precision(), enhance_actionability(), preserve_intent()]; constraints=[maintain_original_scope(), no_scope_creep()]; requirements=[improved_clarity(), enhanced_precision(), preserved_intent()]; output={enhanced_instruction:str}}`",
        "context": {},
    },

    # 1400: Value-Maximizing Instruction Converter
    "1400-a-value_maximizing_pattern": {
        "title": "Value-Maximizing Instruction Converter",
        "interpretation": "Your goal is not to answer or summarize, but to rephrase as a single, explicit, maximally actionable directive, ensuring no loss of technical accuracy or intent. Execute as instruction_converter:",
        "transformation": "`{role=instruction_converter; input=[raw_text:str]; process=[strip_first_person(), convert_to_directive(), enforce_technical_terminology(), preserve_logical_order()]; constraints=[no_information_loss(), no_scope_creep()]; requirements=[output_actionable(), output_in_command_voice(), maximal_conciseness()]; output={instruction:str}}`",
        "context": {}
    },

    # 1450: Synergic Instruction Architect
    "1450-a-instruction_combiner": {
        "title": "Synergic Instruction Architect",
        "interpretation": " Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:",
        "transformation": "`{role=synergic_architect; input=[instruction_a:str, instruction_b:str]; process=[identify_resonance_points(), create_bidirectional_amplification(), synthesize_unified_directive()]; constraints=[no_simple_merging(), inherent_resonance_required()]; requirements=[bidirectional_enhancement(), unified_output()]; output={synthesized_instruction:str}}`",
        "context": {}
    },

    # 1700: Universal Template Constructor
    "1700-a-template_constructor_compact": {
        "title": "Universal Template Constructor",
        "interpretation": "Your goal is not to execute, narrate, or prototype outputs, but to emit one literal, machine-parsable instruction template that conforms exactly to the system schema and is immediately reusable. Execute as template_constructor:",
        "transformation": "`{role=template_constructor; input=[description:str]; process=[extract_core_function(), map_to_schema(), validate_parsability()]; constraints=[literal_output_only(), schema_conformance(), machine_parsable()]; requirements=[immediate_reusability(), exact_conformance()]; output={template:dict}}`",
        "context": {
            "schema_requirements": {
                "structure": ["title", "interpretation", "transformation", "context"],
                "transformation_format": "backticked semicolon-keyed schema",
                "process_verbs": "whitelist only",
                "constraints": "numeric thresholds + format rules",
                "requirements": "declared invariants"
            }
        }
    },

    # 1900: Ruthless Critique
    "1900-a-hard_critique": {
        "title": "Hard Critique",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded parameters. Execute as hard_critique:",
        "transformation": "`{role=hard_critique; input=[claimed_improvement:str]; process=[rephrase_claim(), identify_weak_points(), dismantle_logic(), expose_assumptions()]; constraints=[synchronous_operation(), critical_analysis_only()]; requirements=[ruthless_examination(), logical_dismantling()]; output={critique:str}}`",
        "context": {}
    }
}
